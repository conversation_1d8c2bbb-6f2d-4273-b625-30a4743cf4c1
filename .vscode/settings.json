{
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[mjs]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[yaml]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "css.validate": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "editor.tabSize": 2,
  "editor.formatOnSave": true,
  "eslint.validate": ["javascript"],
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.trimTrailingWhitespace": true,
  "jest.runMode": {
    "type": "watch"
    // "type": "on-save",
    // "testFile": true,
    // "all": true
  },
  // "jest.jestCommandLine": "yarn test",
  "jest.jestCommandLine": "source ~/.zshrc && nvm use && yarn test",
  "jest.shell": "/bin/zsh",
  // "jest.nodeEnv": {
  //   "NVM_DIR": "${env:HOME}/.nvm"
  // },
  "less.validate": false,
  "linthtml.packageManager": "yarn",
  "scss.validate": false
}
