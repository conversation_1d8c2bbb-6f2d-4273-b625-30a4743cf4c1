{"private": false, "name": "webnd-project5-travel-app", "version": "1.0.0", "description": "Travel App - Project 5 for the Front End Web Developer Nanodegree at Udacity ", "license": "CC0-1.0", "author": "<PERSON>", "homepage": "https://github.com/luca-borrione/webnd-project5-travel-app", "repository": {"type": "git", "url": "**************:luca-borrione/webnd-project5-travel-app.git"}, "scripts": {"check-yarn-integrity": "yarn check --integrity", "clean": "rm -rf dist", "compile": "webpack --config webpack.prod.js", "compile:dev": "webpack --config webpack.dev.js", "dev": "yarn clean && webpack-dev-server --config webpack.dev.js --open --port=3000", "format": "npm-run-all --parallel format:*", "format:html": "prettier --write 'src/client/views/**/*.html'", "format:js": "$npm_execpath run lint:js --fix", "format:json": "prettier --write '*.json'", "format:scss": "$npm_execpath run lint:scss --fix", "heroku-postbuild": "yarn clean; yarn compile", "lint": "npm-run-all --parallel lint:*", "lint:html": "linthtml 'src/client/views/**/*.html'", "lint:js": "eslint . --ext .js", "lint:scss": "stylelint 'src/client/styles/**/*.scss'", "prepare": "husky", "start": "node src/server/index.js", "test": "jest"}, "dependencies": {"bent": "7.3.12", "body-parser": "2.2.0", "cors": "2.8.5", "dotenv": "16.5.0", "express": "5.1.0", "file-loader": "6.2.0", "html-loader": "5.1.0", "html-webpack-plugin": "5.6.3", "node-fetch": "2.7.0", "terser-webpack-plugin": "5.3.14"}, "devDependencies": {"@babel/core": "7.27.4", "@babel/plugin-transform-runtime": "7.27.4", "@babel/preset-env": "7.27.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@linthtml/linthtml": "0.10.2", "@linthtml/linthtml-config-recommended": "0.2.0", "@types/jest": "29.5.14", "babel-loader": "10.0.0", "babel-plugin-transform-async-to-promises": "0.8.18", "clean-webpack-plugin": "4.0.0", "css-loader": "7.1.2", "css-minimizer-webpack-plugin": "7.0.2", "eslint": "^9.28.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-import-x": "^4.15.2", "eslint-plugin-jest": "28.13.3", "eslint-plugin-jsx-a11y": "^6.10.2", "globals": "^16.2.0", "husky": "9.1.7", "jest": "30.0.0", "jest-environment-jsdom": "30.0.0", "lint-staged": "16.1.0", "mini-css-extract-plugin": "2.9.2", "npm-run-all": "4.1.5", "postcss": "8.5.5", "prettier": "3.5.3", "prettier-package-json": "2.8.0", "prettier-plugin-sort-json": "4.1.1", "regenerator-runtime": "0.14.1", "sass": "1.89.2", "sass-loader": "16.0.5", "setimmediate": "1.0.5", "style-loader": "4.0.0", "stylelint": "16.20.0", "stylelint-config-standard": "38.0.0", "stylelint-config-standard-scss": "15.0.1", "stylelint-prettier": "5.0.3", "supertest": "7.1.1", "typescript": "5.8.3", "webpack": "5.99.9", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "6.0.1", "webpack-dev-server": "5.2.2", "workbox-webpack-plugin": "7.3.0"}, "engines": {"node": "20.17.0", "npm": "10.2.4", "yarn": "1.22.19"}}