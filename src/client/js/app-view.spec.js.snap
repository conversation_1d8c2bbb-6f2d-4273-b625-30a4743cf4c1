// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`app-view after having launched the app calling removeTrip should correctly update the saved trips view 1`] = `
<section
  class="saved-trips"
/>
`;

exports[`app-view after having launched the app calling removeTrip should correctly update the saved trips view 2`] = `
<section
  class="saved-trips"
>
  
    
  <main
    class="card"
  >
    
      mock-saved-trips-view
    
  </main>
  
  
</section>
`;

exports[`app-view after having launched the app clicking on the save trip button of the search results should correctly call postSaveTrip 1`] = `
Object {
  "trip": Object {
    "currentInfo": Object {
      "timezone": "mock-timezone",
      "weather": Object {
        "description": "mock-description",
        "humidity": "mock-humidity",
        "icon": "mock-icon",
        "observedTimestamp": "mock-observed-timestamp",
        "temperature": "mock-temperature",
        "timezone": "mock-timezone",
        "windSpeed": "mock-wind-speed",
      },
    },
    "departureInfo": Object {
      "dateString": "2021-11-10",
      "weather": Object {
        "description": "mock-description",
        "humidity": "mock-humidity",
        "icon": "mock-icon",
        "windSpeed": "mock-wind-speed",
      },
    },
    "id": 1643068801234,
    "locationInfo": Object {
      "capital": "mock-capital",
      "city": "mock-city",
      "continent": "mock-continent",
      "country": "mock-country",
      "county": "mock-county",
      "currencies": Array [
        Object {
          "code": "cur1",
          "name": "mock-currency1",
        },
        Object {
          "code": "cur2",
          "name": "mock-currency2",
        },
      ],
      "flag": "mock-flag",
      "languages": Array [
        "mock-language1",
        "mock-language2",
      ],
      "latitude": "mock-latitude",
      "longitude": "mock-longitude",
      "offset": "mock-offset",
      "subregion": "mock-subregion",
      "timezone": "mock-timezone",
    },
    "returnInfo": Object {
      "dateString": "2021-11-17",
      "weather": Object {
        "description": "mock-description",
        "humidity": "mock-humidity",
        "icon": "mock-icon",
        "windSpeed": "mock-wind-speed",
      },
    },
    "thumbnail": "mock-image-url",
  },
}
`;

exports[`app-view after having launched the app clicking on the save trip button of the search results should correctly update the saved trips view 1`] = `
<section
  class="saved-trips"
/>
`;

exports[`app-view after having launched the app clicking on the save trip button of the search results should correctly update the saved trips view 2`] = `
<section
  class="saved-trips"
>
  
    
  <main
    class="card"
  >
    
      mock-saved-trips-view
    
  </main>
  
  
</section>
`;

exports[`app-view after having launched the app receiving the data of the search should correctly call renderResultsView 1`] = `
Object {
  "currentInfo": Object {
    "timezone": "mock-timezone",
    "weather": Object {
      "description": "mock-description",
      "humidity": "mock-humidity",
      "icon": "mock-icon",
      "observedTimestamp": "mock-observed-timestamp",
      "temperature": "mock-temperature",
      "timezone": "mock-timezone",
      "windSpeed": "mock-wind-speed",
    },
  },
  "departureInfo": Object {
    "dateString": "2021-11-10",
    "weather": Object {
      "description": "mock-description",
      "humidity": "mock-humidity",
      "icon": "mock-icon",
      "windSpeed": "mock-wind-speed",
    },
  },
  "id": 1643068801234,
  "locationInfo": Object {
    "capital": "mock-capital",
    "city": "mock-city",
    "continent": "mock-continent",
    "country": "mock-country",
    "county": "mock-county",
    "currencies": Array [
      Object {
        "code": "cur1",
        "name": "mock-currency1",
      },
      Object {
        "code": "cur2",
        "name": "mock-currency2",
      },
    ],
    "flag": "mock-flag",
    "languages": Array [
      "mock-language1",
      "mock-language2",
    ],
    "latitude": "mock-latitude",
    "longitude": "mock-longitude",
    "offset": "mock-offset",
    "subregion": "mock-subregion",
    "timezone": "mock-timezone",
  },
  "returnInfo": Object {
    "dateString": "2021-11-17",
    "weather": Object {
      "description": "mock-description",
      "humidity": "mock-humidity",
      "icon": "mock-icon",
      "windSpeed": "mock-wind-speed",
    },
  },
  "thumbnail": "mock-image-url",
}
`;

exports[`app-view after having launched the app receiving the data of the search should correctly update the results view 1`] = `
<section
  class="results"
>
  
        
  <section
    class="saved-trips"
  />
</section>
`;

exports[`app-view after having launched the app receiving the data of the search should correctly update the results view 2`] = `
<section
  class="results"
>
  
    
  <main
    class="card"
  >
    
      mock-results-view
      
    <button
      class="card__button"
      type="button"
    />
    
    
  </main>
  
  
</section>
`;

exports[`app-view launching the app should correctly update the saved trips view 1`] = `
<section
  class="saved-trips"
>
  
      
</section>
`;

exports[`app-view launching the app should correctly update the saved trips view 2`] = `
<section
  class="saved-trips"
>
  
    
  <main
    class="card"
  >
    
      mock-saved-trips-view
    
  </main>
  
  
</section>
`;
