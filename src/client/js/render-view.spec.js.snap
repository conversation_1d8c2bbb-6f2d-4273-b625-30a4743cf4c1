// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renderResultsView should correctly render the view, when both the departure and return weather forecasts are undefined 1`] = `
"
<main class="card">
  
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

  <article class="card__info">
          <div class="card__info-entry">
            <div class="label">Continent:</div>
            <div class="value">mock-continent</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Subregion:</div>
            <div class="value">mock-subregion</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Country:</div>
            <div class="value">mock-country</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">County:</div>
            <div class="value">mock-county</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Capital:</div>
            <div class="value">mock-capital</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Currency:</div>
            <div class="value">mock-currency1 (cur1), mock-currency2 (cur2)</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Language:</div>
            <div class="value">mock-language1, mock-language2</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Timezone:</div>
            <div class="value">mock-timezone</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Offset:</div>
            <div class="value">mock-offset</div>
          </div>
        </article>
  
  <article class="infobox current">
    <div class="infobox__title">Last Observed</div>
    <div class="infobox__date">mock-observed-timestamp-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-current-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 4% W 10&#x33A7;
    </div>
    <div class="weather__description">mock-current-description</div>
  </aside>
  </article>
  
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  </article>
  
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  </article>
  
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
  
  <button class="card__button" type="button">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Save Trip</span>
  </button>
  
</main>"
`;

exports[`renderResultsView should correctly render the view, when the county is mock-county 1`] = `
"
<main class="card">
  
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

  <article class="card__info">
          <div class="card__info-entry">
            <div class="label">Continent:</div>
            <div class="value">mock-continent</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Subregion:</div>
            <div class="value">mock-subregion</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Country:</div>
            <div class="value">mock-country</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">County:</div>
            <div class="value">mock-county</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Capital:</div>
            <div class="value">mock-capital</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Currency:</div>
            <div class="value">mock-currency1 (cur1), mock-currency2 (cur2)</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Language:</div>
            <div class="value">mock-language1, mock-language2</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Timezone:</div>
            <div class="value">mock-timezone</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Offset:</div>
            <div class="value">mock-offset</div>
          </div>
        </article>
  
  <article class="infobox current">
    <div class="infobox__title">Last Observed</div>
    <div class="infobox__date">mock-observed-timestamp-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-current-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 4% W 10&#x33A7;
    </div>
    <div class="weather__description">mock-current-description</div>
  </aside>
  </article>
  
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
  
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
  
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
  
  <button class="card__button" type="button">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Save Trip</span>
  </button>
  
</main>"
`;

exports[`renderResultsView should correctly render the view, when the county is undefined 1`] = `
"
<main class="card">
  
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

  <article class="card__info">
          <div class="card__info-entry">
            <div class="label">Continent:</div>
            <div class="value">mock-continent</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Subregion:</div>
            <div class="value">mock-subregion</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Country:</div>
            <div class="value">mock-country</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">County:</div>
            <div class="value">mock-county</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Capital:</div>
            <div class="value">mock-capital</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Currency:</div>
            <div class="value">mock-currency1 (cur1), mock-currency2 (cur2)</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Language:</div>
            <div class="value">mock-language1, mock-language2</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Timezone:</div>
            <div class="value">mock-timezone</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Offset:</div>
            <div class="value">mock-offset</div>
          </div>
        </article>
  
  <article class="infobox current">
    <div class="infobox__title">Last Observed</div>
    <div class="infobox__date">mock-observed-timestamp-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-current-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 4% W 10&#x33A7;
    </div>
    <div class="weather__description">mock-current-description</div>
  </aside>
  </article>
  
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
  
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
  
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
  
  <button class="card__button" type="button">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Save Trip</span>
  </button>
  
</main>"
`;

exports[`renderResultsView should correctly render the view, when the return weather forecast is undefined 1`] = `
"
<main class="card">
  
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

  <article class="card__info">
          <div class="card__info-entry">
            <div class="label">Continent:</div>
            <div class="value">mock-continent</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Subregion:</div>
            <div class="value">mock-subregion</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Country:</div>
            <div class="value">mock-country</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">County:</div>
            <div class="value">mock-county</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Capital:</div>
            <div class="value">mock-capital</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Currency:</div>
            <div class="value">mock-currency1 (cur1), mock-currency2 (cur2)</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Language:</div>
            <div class="value">mock-language1, mock-language2</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Timezone:</div>
            <div class="value">mock-timezone</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Offset:</div>
            <div class="value">mock-offset</div>
          </div>
        </article>
  
  <article class="infobox current">
    <div class="infobox__title">Last Observed</div>
    <div class="infobox__date">mock-observed-timestamp-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-current-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 4% W 10&#x33A7;
    </div>
    <div class="weather__description">mock-current-description</div>
  </aside>
  </article>
  
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
  
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  </article>
  
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
  
  <button class="card__button" type="button">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Save Trip</span>
  </button>
  
</main>"
`;

exports[`renderResultsView should correctly render the view, when the thumbnail is mock-thumbnail 1`] = `
"
<main class="card">
  
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

  <article class="card__info">
          <div class="card__info-entry">
            <div class="label">Continent:</div>
            <div class="value">mock-continent</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Subregion:</div>
            <div class="value">mock-subregion</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Country:</div>
            <div class="value">mock-country</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">County:</div>
            <div class="value">mock-county</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Capital:</div>
            <div class="value">mock-capital</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Currency:</div>
            <div class="value">mock-currency1 (cur1), mock-currency2 (cur2)</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Language:</div>
            <div class="value">mock-language1, mock-language2</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Timezone:</div>
            <div class="value">mock-timezone</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Offset:</div>
            <div class="value">mock-offset</div>
          </div>
        </article>
  
  <article class="infobox current">
    <div class="infobox__title">Last Observed</div>
    <div class="infobox__date">mock-observed-timestamp-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-current-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 4% W 10&#x33A7;
    </div>
    <div class="weather__description">mock-current-description</div>
  </aside>
  </article>
  
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
  
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
  
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
  
  <button class="card__button" type="button">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Save Trip</span>
  </button>
  
</main>"
`;

exports[`renderResultsView should correctly render the view, when the thumbnail is undefined 1`] = `
"
<main class="card">
  
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="src/client/assets/placeholder-destination-thumb.jpg"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

  <article class="card__info">
          <div class="card__info-entry">
            <div class="label">Continent:</div>
            <div class="value">mock-continent</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Subregion:</div>
            <div class="value">mock-subregion</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Country:</div>
            <div class="value">mock-country</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">County:</div>
            <div class="value">mock-county</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Capital:</div>
            <div class="value">mock-capital</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Currency:</div>
            <div class="value">mock-currency1 (cur1), mock-currency2 (cur2)</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Language:</div>
            <div class="value">mock-language1, mock-language2</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Timezone:</div>
            <div class="value">mock-timezone</div>
          </div>
        
          <div class="card__info-entry">
            <div class="label">Offset:</div>
            <div class="value">mock-offset</div>
          </div>
        </article>
  
  <article class="infobox current">
    <div class="infobox__title">Last Observed</div>
    <div class="infobox__date">mock-observed-timestamp-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-current-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 4% W 10&#x33A7;
    </div>
    <div class="weather__description">mock-current-description</div>
  </aside>
  </article>
  
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
  
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
  
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
  
  <button class="card__button" type="button">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Save Trip</span>
  </button>
  
</main>"
`;

exports[`renderSavedTripsView should correctly render the view, when both the departure and return weather forecasts are undefined 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the return weather forecast is undefined 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the thumbnail is mock-thumbnail 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the thumbnail is undefined 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="src/client/assets/placeholder-destination-thumb.jpg"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the trip is expired 1`] = `
"
        
        <main class="card expired" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-right"><span>expired</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the trip is in future 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the trip is today 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>today</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when the trip is tomorrow 1`] = `
"
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>tomorrow</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;

exports[`renderSavedTripsView should correctly render the view, when there are multiple saved trips 1`] = `
"
        
        
        <main class="card" id="mock-trip-id-1">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-1)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      
        <main class="card" id="mock-trip-id-2">
          
    <div class="ribbon ribbon-top-left"><span>in 2 days</span></div>
  
          
<figure class="card__image">
  <img
    class="card__image-thumbnail"
    src="mock-thumbnail-url"
    alt="view of the destination"
  />
  <figcaption class="card__image-caption">
    <span class="card__image-caption-flag">mock-flag</span>
    <span class="card__image-caption-city">mock-city</span>
  </figcaption>
</figure>

          
  <article class="infobox departure">
    <div class="infobox__title">Departure</div>
    <div class="infobox__date">mock-departure-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-departure-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 14% W 20&#x33A7;
    </div>
    <div class="weather__description">mock-departure-description</div>
  </aside>
  </article>
          
  <article class="infobox return">
    <div class="infobox__title">Return</div>
    <div class="infobox__date">mock-return-date-formatted</div>
    
  <aside class="weather">
    <img
    class="weather__icon"
    src="src/client/assets/weather-icons/a01d.png"
    alt="weather icon"
    />
    <div class="weather__temperature">
      <span>mock-return-temperature<sup>&#8451;</sup></span>
    </div>
    <div class="weather__extra-info">
    H 24% W 30&#x33A7;
    </div>
    <div class="weather__description">mock-return-description</div>
  </aside>
  </article>
          
  <aside class="days-diff">
    <div class="days-diff__title">Length:</div>
    <div class="days-diff__value">1 days</div>
  </aside>
          
  <button class="card__button" type="button" onClick="return Client.removeTrip(mock-trip-id-2)">
    <i class="icon-heart-empty"></i>
    <i class="icon-heart"></i>
    <span>Remove Trip</span>
  </button>
  
        </main>
      "
`;
