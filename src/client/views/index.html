<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport" />
    <title>Travel App</title>
  </head>

  <body>
    <header>
      <section class="banner">
        <h2 id="banner__text">Find Your Next Adventure</h2>
      </section>
    </header>

    <aside class="spinner">
      <img src="../assets/spinner.gif" alt="Loading..." />
    </aside>

    <main class="main-area hide">
      <section class="search">
        <form class="search-form" name="search">
          <label for="search-form__destination">
            To
            <input id="search-form__destination" type="text" required />
          </label>
          <label for="search-form__departure">
            From
            <input id="search-form__departure" type="text" required />
          </label>
          <label for="search-form__departure-date">
            Departure
            <input
              id="search-form__departure-date"
              type="date"
              required
              placeholder="yyyy/mm/dd"
              pattern="\d{4}-\d{2}-\d{2}"
            />
          </label>
          <label for="search-form__return-date">
            Return
            <input
              id="search-form__return-date"
              type="date"
              required
              placeholder="yyyy/mm/dd"
              pattern="\d{4}-\d{2}-\d{2}"
            />
          </label>
          <button type="submit" class="search-form__submit-button">
            <i class="icon-spinner animate-spin"></i>
            <span>Search</span>
          </button>
        </form>
      </section>
      <section class="results"></section>
      <section class="saved-trips"></section>
    </main>

    <!-- Register Service Workers -->
    <script>
      window.ENABLE_SERVICE_WORKER = false; // This will be replaced during build
      // Check that service workers are supported
      if ('serviceWorker' in navigator && window.ENABLE_SERVICE_WORKER) {
        // Use the window load event to keep the page load performant
        window.addEventListener('load', () => {
          navigator.serviceWorker
            .register('/service-worker.js')
            .then((registration) => {
              // Registration was successful
              console.log('ServiceWorker registration successful with scope: ', registration.scope);
            })
            .catch((err) => {
              // registration failed :(
              console.error('ServiceWorker registration failed: ', err);
            });
        });
      }
    </script>
  </body>
</html>
