@use 'core/variables';
@import 'https://fonts.googleapis.com/css2?family=Asap:wght@400;700&family=Ubuntu:wght@400;700&family=Poppins:ital,wght@0,300;0,400;0,500;1,300;1,500&display=swap';

@font-face {
  font-family: Fontello;
  src: url('../assets/fonts/fontello.eot?39098809');
  src:
    url('../assets/fonts/fontello.eot?39098809#iefix') format('embedded-opentype'),
    url('../assets/fonts/fontello.woff2?39098809') format('woff2'),
    url('../assets/fonts/fontello.woff?39098809') format('woff'),
    url('../assets/fonts/fontello.ttf?39098809') format('truetype'),
    url('../assets/fonts/fontello.svg?39098809#fontello') format('svg');
  font-weight: normal;
  font-style: normal;
}

/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */

/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */

/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?39088227#fontello') format('svg');
  }
}
*/
[class^='icon-']::before,
[class*=' icon-']::before {
  font-family: Fontello; // stylelint-disable-line font-family-no-missing-generic-family-keyword
  font-style: normal;
  font-weight: normal;
  speak: never;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: 0.2em;
  text-align: center;

  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes */
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */

  /* remove if not needed */
  margin-left: 0.2em;

  /* you can be more comfortable with increased icons size */

  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */

  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-heart::before {
  content: '\e800';
}

.icon-heart-empty::before {
  content: '\e801';
}

.icon-trash-empty::before {
  content: '\e802';
}

.icon-cancel::before {
  content: '\e803';
}

.icon-cancel-circled::before {
  content: '\e804';
}

.icon-cancel-circled2::before {
  content: '\e805';
}

.icon-spinner::before {
  content: '\f110';
}

.icon-trash::before {
  content: '\f1f8';
}

.animate-spin {
  animation: spin 2s infinite linear;
  display: inline-block;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(359deg);
  }
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: variables.$primary-font-family;
  font-size: 1.3rem;
  color: variables.$primary-font-color;
  background-color: variables.$main-bg-color;
}

button {
  width: 100%;
  font-size: 1.05rem;
  border-radius: 0.3rem;
  font-weight: 500;
  text-transform: uppercase;
  line-height: 1;
  font-family: variables.$primary-font-family;

  // transition: all ease 0.25s;

  &:hover {
    cursor: pointer;
  }
}
