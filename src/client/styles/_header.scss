@use 'core/variables';

/* Mobile First */
.banner {
  display: grid;
  place-content: center center;
  background-image: url('../assets/lighthouse.jpg');
  background-size: auto;
  background-position-y: 52%;
  background-position-x: 75%;
  background-repeat: no-repeat;
  height: 10rem;

  &__text {
    color: variables.$secondary-font-color;
    font-size: 1.35rem;
    font-weight: 100;
    margin: 0;
    padding: 0;
  }
}

/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
@media (width >= 600px) {
  .banner {
    height: 12em;
    background-position-y: 63%;

    &__text {
      // color: yellow;
      font-size: 1.5em;
      font-weight: 400;
    }
  }
}

/* tablet, landscape iPad, lo-res laptops ands desktops */
@media (width >= 961px) {
  .banner {
    background-size: cover;
    background-position-y: 61%;

    &__text {
      // color: aqua;
      font-size: 2em;
      font-weight: 600;
    }
  }
}
