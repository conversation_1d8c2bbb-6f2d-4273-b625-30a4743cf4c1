@use 'sass:color';
@use 'core/variables';
@use 'layout';
@use 'header';
@use 'main/main';

.spinner {
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;

  &.hide {
    display: none;
  }
}

.ribbon {
  width: 150px;
  height: 150px;
  overflow: hidden;
  position: absolute;
  z-index: 1;
  filter: drop-shadow(1px 2px 3px black);
  opacity: 0.95;

  .expired & {
    filter: drop-shadow(1px 2px 3px black) hue-rotate(45deg);
  }

  &::before,
  &::after {
    position: absolute;
    z-index: -1;
    content: '';
    display: block;
    border: 5px solid color.scale(variables.$primary-bg-color, $lightness: -40%);
  }

  & span {
    position: absolute;
    display: block;
    width: 225px;
    padding: 15px 0;
    background-color: variables.$primary-bg-color;
    box-shadow: 0 5px 10px rgb(0 0 0 / 10%);
    color: #fff;
    font:
      700 18px/1 Lato,
      sans-serif;
    text-shadow: 0 1px 1px rgb(0 0 0 / 20%);
    text-transform: uppercase;
    text-align: center;
    opacity: 0.9;
  }

  &.ribbon-top-left {
    top: -10px;
    left: -10px;

    &::before,
    &::after {
      border-top-color: transparent;
      border-left-color: transparent;
    }

    &::before {
      top: 0;
      right: 0;
    }

    &::after {
      bottom: 0;
      left: 0;
    }

    & span {
      right: -25px;
      top: 30px;
      transform: rotate(-45deg);
    }
  }

  &.ribbon-top-right {
    top: -10px;
    right: -10px;

    &::before,
    &::after {
      border-top-color: transparent;
      border-right-color: transparent;
    }

    &::before {
      top: 0;
      left: 0;
    }

    &::after {
      bottom: 0;
      right: 0;
    }

    & span {
      left: -25px;
      top: 30px;
      transform: rotate(45deg);
    }
  }
}
