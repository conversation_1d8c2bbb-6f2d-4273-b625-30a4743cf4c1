@use 'search';
@use 'card';

main {
  section {
    max-width: 960px;
    margin: 50px auto;
    padding: 0 30px;

    &.saved-trips {
      display: grid;
      align-items: self-start;
      gap: 1rem;
    }
  }

  &.hide {
    display: none;
  }
}

/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
@media (width >= 600px) {
  main {
    section {
      &.saved-trips {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

/* tablet, landscape iPad, lo-res laptops ands desktops */
@media (width >= 961px) {
  main {
    section {
      &.saved-trips {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}
