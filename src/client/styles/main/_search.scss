@use 'sass:color';
@use '../core/variables';

/* Mobile First */
.search {
  margin-top: -3.2rem;

  &-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    border-radius: 0.5rem;
    padding: 1.3rem;
    background: variables.$main-bg-color;
    box-shadow: 0 0.07rem 0.15rem variables.$primary-bg-color;

    & label {
      margin-bottom: 0.8rem;
      font-size: 0.8em;
      padding: 0;
      width: 100%;
    }

    & input {
      margin: 0;
      height: 3rem;
      background: color.scale(variables.$primary-bg-color, $lightness: 85%);
      border: 0.15rem solid color.scale(variables.$primary-bg-color, $lightness: 85%);
      width: 100%;
      padding: 1rem 0.6rem;
      margin-bottom: 0;
      border-radius: 0.4rem;
      font-family: variables.$primary-font-family;
      font-size: 1.1rem;

      &:focus-visible {
        outline-color: variables.$primary-bg-color;
      }
    }

    &__submit-button {
      border: 0.1rem;
      color: variables.$secondary-font-color;
      background: variables.$primary-bg-color;
      padding: 1.15rem 1.3rem;

      &:hover {
        background: color.scale(variables.$primary-bg-color, $lightness: -20%);
      }

      & .icon-spinner {
        display: none;
      }

      &.loading {
        & span {
          display: none;
        }

        & .icon-spinner {
          display: block;
        }
      }
    }
  }
}

/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
@media (width >= 600px) {
  .search {
    margin-top: -4.5rem;

    &-form {
      & label {
        width: 50%;
        padding: 0.5rem;
      }

      &__submit-button {
        width: 10rem;
        margin: auto;
      }
    }
  }
}

// /* tablet, landscape iPad, lo-res laptops ands desktops */
// @media (min-width: 961px) {
// }
