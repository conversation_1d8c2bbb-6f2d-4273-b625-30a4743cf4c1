@use 'sass:color';
@use '../core/variables';

$red-200: hsl(360deg 77% 78%);
$red-300: hsl(360deg 71% 66%);
$red-400: hsl(360deg 64% 55%);

.card {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  background: variables.$main-bg-color;
  border-radius: 0.5rem;
  box-shadow: 0 0.07rem 0.15rem variables.$primary-bg-color;

  .saved-trips & {
    margin-bottom: 1.5rem;
  }

  & > * {
    width: 100%;
  }

  &.expired > * {
    filter: grayscale(100%);
  }

  &__image {
    grid-area: card-image;
    margin: 0;
    background-color: color.scale(variables.$primary-bg-color, $lightness: 85%);

    &-thumbnail {
      width: 100%;
      border-top-right-radius: 0.5rem;
      border-top-left-radius: 0.5rem;
      object-fit: cover;
    }

    &-caption {
      position: relative;
      top: -0.3rem;
      display: flex;

      &-flag {
        margin-left: auto;
        font-size: 1.6rem;
        padding-right: 0.2rem;
      }

      &-city {
        margin-right: auto;
        text-transform: capitalize;
        font-weight: bold;
        padding-left: 0.2rem;
        align-self: center;
      }
    }
  }

  &__info {
    grid-area: card-info;
    padding: 0.3rem;
    margin-left: auto;
    margin-right: auto;
    width: initial;
    margin-top: 1.5rem;

    .label {
      font-size: 1.1rem;
      font-weight: 500;
    }

    .value {
      font-size: 1.1rem;
      align-self: center;
      margin-bottom: 0.5rem;
      overflow: hidden;
    }
  }

  .infobox {
    width: 90%;
    margin: auto;
    margin-bottom: 1.5rem;
    display: grid;
    grid-template:
      'infobox-title' 1fr
      'infobox-date' 1fr
      'weather' auto / 1fr;

    &.current {
      grid-area: infobox-current;
    }

    &.departure {
      grid-area: infobox-departure;
      margin-top: 1.5rem;
    }

    &.return {
      grid-area: infobox-return;
    }

    &__title {
      grid-area: infobox-title;
      margin: auto;
      font-weight: bold;
    }

    &__date {
      grid-area: infobox-date;
      margin: auto;
    }

    .weather {
      grid-area: weather;
      display: grid;
      grid-template:
        'weather-icon weather-temperature' auto
        'weather-icon weather-extra-info' auto
        'weather-description weather-description' 1fr / 1fr 2fr;

      &__icon {
        grid-area: weather-icon;
        width: 85%;
        height: auto;
        margin: auto;
      }

      &__temperature {
        grid-area: weather-temperature;
        margin: auto;
        display: flex;
        flex-direction: column;
        font-size: 1.5rem;
      }

      &__extra-info {
        grid-area: weather-extra-info;
        margin: auto;
        font-size: 1.2rem;
      }

      &__description {
        grid-area: weather-description;
        margin: auto;
      }
    }
  }

  .days-diff {
    grid-area: days-diff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;

    &__title {
      font-weight: bold;
    }
  }

  &__button {
    grid-area: card-button;
    background-color: color.scale(variables.$primary-bg-color, $lightness: 85%);
    border: 0;
    font-size: 1.1rem;
    height: 3rem;
    border-bottom-right-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;

    & i {
      color: $red-400;

      &.icon-heart-empty {
        display: inline-block;
      }

      &.icon-heart {
        display: none;
      }
    }

    &:hover {
      background-color: color.scale(variables.$primary-bg-color, $lightness: 65%);

      & .icon-heart-empty {
        display: none;
      }

      & .icon-heart {
        display: inline-block;
      }
    }
  }
}

/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
@media (width >= 600px) {
  .results .card {
    display: grid;
    align-items: start;
    grid-template-areas:
      'card-image card-image infobox-current'
      'infobox-departure days-diff infobox-return'
      'card-info card-info card-info'
      'card-button card-button card-button';

    .infobox {
      width: 100%;
      margin: initial;

      &.current {
        align-self: center;

        .infobox__date {
          text-align: center;
          font-size: 1.2rem;
        }
      }

      &.departure,
      &.return {
        margin-top: 1rem;
      }
    }

    .days-diff {
      margin-top: 1rem;
    }

    &__info {
      display: grid;
      align-items: self-start;
      grid-template-columns: repeat(3, 1fr);
      margin-top: 1.5rem;
      width: 100%;

      &-entry {
        padding: 0.2rem;
      }
    }

    &__image {
      filter: drop-shadow(1px 2px 3px #ccc);
      border-radius: 0.5rem;
    }
  }

  .saved-trips .card {
    &__image {
      &-thumbnail {
        height: 12rem;
      }
    }
  }
}

/* tablet, landscape iPad, lo-res laptops ands desktops */
@media (width >= 961px) {
  .results .card {
    display: grid;
    align-items: start;
    grid-template-areas:
      'card-image card-image card-info card-info'
      'infobox-current infobox-departure days-diff infobox-return'
      'card-button card-button card-button card-button';
    grid-template-columns: 31% 28% auto 31%;

    &__info {
      grid-template-columns: repeat(2, 1fr);
      margin: initial;
      padding: initial;
      align-self: center;

      &-entry {
        padding: initial;
        padding-left: 0.4rem;
        padding-right: 0.4rem;
        padding-bottom: 0.4rem;
      }
    }

    .infobox {
      margin-bottom: 1rem;

      &.current {
        margin-top: 1rem;
      }
    }
  }
}
